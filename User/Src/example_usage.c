/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port - Usage Example
 */
#include "example_usage.h"
#include "page_manager.h"
#include "pm_log.h"

/* 示例页面类型 */
typedef struct {
    pm_page_base_t base; // 继承页面基类
    lv_obj_t *label;     // 页面标签
    int page_data;       // 页面私有数据
} example_page_t;

/* 页面虚函数表 */
static pm_page_vtable_t home_page_vtable;
static pm_page_vtable_t settings_page_vtable;
static pm_page_vtable_t about_page_vtable;

/* 全局变量 */
static pm_page_manager_t *g_page_manager = NULL;
static pm_page_factory_t *g_page_factory = NULL;

/* ========== 主页面实现 ========== */

static void home_page_on_view_load(pm_page_base_t *self) {
    example_page_t *page = (example_page_t *) self;

    PM_LOG_INFO("Home page loading...");

    /* 创建页面内容 */
    page->label = lv_label_create(self->root);
    lv_label_set_text(page->label, "Home Page\nPress to Settings");
    lv_obj_center(page->label);

    /* 添加点击事件 */
    lv_obj_add_flag(self->root, LV_OBJ_FLAG_CLICKABLE);

    page->page_data = 100; // 设置页面数据
}

static void home_page_on_view_did_appear(pm_page_base_t *self) { PM_LOG_INFO("Home page appeared"); }

static void home_page_on_view_will_disappear(pm_page_base_t *self) { PM_LOG_INFO("Home page will disappear"); }

static void home_page_on_view_unload(pm_page_base_t *self) { PM_LOG_INFO("Home page unloading..."); }

/* ========== 设置页面实现 ========== */

static void settings_page_on_view_load(pm_page_base_t *self) {
    example_page_t *page = (example_page_t *) self;

    PM_LOG_INFO("Settings page loading...");

    /* 创建页面内容 */
    page->label = lv_label_create(self->root);
    lv_label_set_text(page->label, "Settings Page\nPress to About");
    lv_obj_center(page->label);

    /* 添加点击事件 */
    lv_obj_add_flag(self->root, LV_OBJ_FLAG_CLICKABLE);

    page->page_data = 200; // 设置页面数据
}

static void settings_page_on_view_did_appear(pm_page_base_t *self) { PM_LOG_INFO("Settings page appeared"); }

static void settings_page_on_view_will_disappear(pm_page_base_t *self) { PM_LOG_INFO("Settings page will disappear"); }

static void settings_page_on_view_unload(pm_page_base_t *self) { PM_LOG_INFO("Settings page unloading..."); }

/* ========== 关于页面实现 ========== */

static void about_page_on_view_load(pm_page_base_t *self) {
    example_page_t *page = (example_page_t *) self;

    PM_LOG_INFO("About page loading...");

    /* 创建页面内容 */
    page->label = lv_label_create(self->root);
    lv_label_set_text(page->label, "About Page\nC Language Port\nDrag to go back");
    lv_obj_center(page->label);

    page->page_data = 300; // 设置页面数据
}

static void about_page_on_view_did_appear(pm_page_base_t *self) { PM_LOG_INFO("About page appeared"); }

static void about_page_on_view_will_disappear(pm_page_base_t *self) { PM_LOG_INFO("About page will disappear"); }

static void about_page_on_view_unload(pm_page_base_t *self) { PM_LOG_INFO("About page unloading..."); }

/* ========== 页面工厂实现 ========== */

static pm_page_base_t *example_page_factory_create(const char *name) {
    example_page_t *page = NULL;

    PM_LOG_INFO("Creating page: %s", name);

    if (strcmp(name, "HomePage") == 0) {
        page = (example_page_t *) malloc(sizeof(example_page_t));
        if (page != NULL) {
            memset(page, 0, sizeof(example_page_t));
            /* 初始化基类 */
            page->base = *pm_page_base_create();
            /* 设置虚函数表 */
            pm_page_base_set_vtable(&page->base, &home_page_vtable);
        }
    } else if (strcmp(name, "SettingsPage") == 0) {
        page = (example_page_t *) malloc(sizeof(example_page_t));
        if (page != NULL) {
            memset(page, 0, sizeof(example_page_t));
            page->base = *pm_page_base_create();
            pm_page_base_set_vtable(&page->base, &settings_page_vtable);
        }
    } else if (strcmp(name, "AboutPage") == 0) {
        page = (example_page_t *) malloc(sizeof(example_page_t));
        if (page != NULL) {
            memset(page, 0, sizeof(example_page_t));
            page->base = *pm_page_base_create();
            pm_page_base_set_vtable(&page->base, &about_page_vtable);
        }
    }

    if (page == NULL) {
        PM_LOG_ERROR("Failed to create page: %s", name);
        return NULL;
    }

    PM_LOG_INFO("Page created successfully: %s", name);
    return &page->base;
}

/* ========== 事件处理 ========== */

static void page_event_handler(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *obj = lv_event_get_target(e);

    if (code == LV_EVENT_CLICKED) {
        pm_page_base_t *page = (pm_page_base_t *) lv_obj_get_user_data(obj);
        if (page == NULL) { return; }

        const char *page_name = pm_page_base_get_name(page);

        if (strcmp(page_name, "HomePage") == 0) {
            /* 从主页跳转到设置页 */
            pm_page_manager_push(g_page_manager, "SettingsPage", NULL);
        } else if (strcmp(page_name, "SettingsPage") == 0) {
            /* 从设置页跳转到关于页 */
            pm_page_manager_push(g_page_manager, "AboutPage", NULL);
        }
    }
}

/* ========== 初始化函数 ========== */

static void init_vtables() {
    /* 初始化主页虚函数表 */
    memset(&home_page_vtable, 0, sizeof(pm_page_vtable_t));
    home_page_vtable.on_view_load = home_page_on_view_load;
    home_page_vtable.on_view_did_appear = home_page_on_view_did_appear;
    home_page_vtable.on_view_will_disappear = home_page_on_view_will_disappear;
    home_page_vtable.on_view_unload = home_page_on_view_unload;

    /* 初始化设置页虚函数表 */
    memset(&settings_page_vtable, 0, sizeof(pm_page_vtable_t));
    settings_page_vtable.on_view_load = settings_page_on_view_load;
    settings_page_vtable.on_view_did_appear = settings_page_on_view_did_appear;
    settings_page_vtable.on_view_will_disappear = settings_page_on_view_will_disappear;
    settings_page_vtable.on_view_unload = settings_page_on_view_unload;

    /* 初始化关于页虚函数表 */
    memset(&about_page_vtable, 0, sizeof(pm_page_vtable_t));
    about_page_vtable.on_view_load = about_page_on_view_load;
    about_page_vtable.on_view_did_appear = about_page_on_view_did_appear;
    about_page_vtable.on_view_will_disappear = about_page_on_view_will_disappear;
    about_page_vtable.on_view_unload = about_page_on_view_unload;
}

/* ========== 主函数 ========== */

int example_page_manager_init() {
    PM_LOG_INFO("Initializing Page Manager Example...");

    /* 初始化虚函数表 */
    init_vtables();

    /* 创建页面工厂 */
    g_page_factory = pm_page_factory_create();
    if (g_page_factory == NULL) {
        PM_LOG_ERROR("Failed to create page factory");
        return -1;
    }

    /* 设置页面创建函数 */
    pm_page_factory_set_create_func(g_page_factory, example_page_factory_create);

    /* 创建页面管理器 */
    g_page_manager = pm_page_manager_create(g_page_factory);
    if (g_page_manager == NULL) {
        PM_LOG_ERROR("Failed to create page manager");
        pm_page_factory_destroy(g_page_factory);
        return -1;
    }

    /* 设置全局动画 */
    pm_page_manager_set_global_load_anim_type(
            g_page_manager,
            PM_LOAD_ANIM_OVER_LEFT,
            500,
            lv_anim_path_ease_out
            );

    /* 安装页面 */
    if (!pm_page_manager_install(g_page_manager, "HomePage", "HomePage")) {
        PM_LOG_ERROR("Failed to install HomePage");
        return -1;
    }

    if (!pm_page_manager_install(g_page_manager, "SettingsPage", "SettingsPage")) {
        PM_LOG_ERROR("Failed to install SettingsPage");
        return -1;
    }

    if (!pm_page_manager_install(g_page_manager, "AboutPage", "AboutPage")) {
        PM_LOG_ERROR("Failed to install AboutPage");
        return -1;
    }

    /* 推入主页作为根页面 */
    if (!pm_page_manager_push(g_page_manager, "HomePage", NULL)) {
        PM_LOG_ERROR("Failed to push HomePage");
        return -1;
    }

    /* 添加全局事件处理器 */
    lv_obj_add_event_cb(lv_scr_act(), page_event_handler, LV_EVENT_ALL, NULL);

    PM_LOG_INFO("Page Manager Example initialized successfully!");
    return 0;
}

void example_page_manager_deinit(void) {
    PM_LOG_INFO("Deinitializing Page Manager Example...");

    if (g_page_manager != NULL) {
        pm_page_manager_destroy(g_page_manager);
        g_page_manager = NULL;
    }

    if (g_page_factory != NULL) {
        pm_page_factory_destroy(g_page_factory);
        g_page_factory = NULL;
    }

    PM_LOG_INFO("Page Manager Example deinitialized");
}