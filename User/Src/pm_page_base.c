/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port - Page Base Implementation
 */
#include "page_manager.h"
#include "pm_log.h"

/* 默认虚函数表 */
static pm_page_vtable_t default_vtable = {
    .on_custom_attr_config = NULL,
    .on_view_load = NULL,
    .on_view_did_load = NULL,
    .on_view_will_appear = NULL,
    .on_view_did_appear = NULL,
    .on_view_will_disappear = NULL,
    .on_view_did_disappear = NULL,
    .on_view_unload = NULL,
    .on_view_did_unload = NULL
};

/**
 * @brief  创建页面基类
 * @retval 页面基类指针，失败返回NULL
 */
pm_page_base_t* pm_page_base_create(void)
{
    pm_page_base_t* page = (pm_page_base_t*)malloc(sizeof(pm_page_base_t));
    if (page == NULL) {
        PM_LOG_ERROR("Failed to allocate memory for page base");
        return NULL;
    }
    
    /* 初始化成员变量 */
    memset(page, 0, sizeof(pm_page_base_t));
    
    /* 设置默认虚函数表 */
    page->vtable = &default_vtable;
    
    /* 初始化私有数据 */
    page->priv.state = PM_PAGE_STATE_IDLE;
    page->priv.req_enable_cache = false;
    page->priv.req_disable_auto_cache = false;
    page->priv.is_disable_auto_cache = false;
    page->priv.is_cached = false;
    page->priv.stash.ptr = NULL;
    page->priv.stash.size = 0;
    page->priv.anim.is_enter = false;
    page->priv.anim.is_busy = false;
    page->priv.anim.attr.type = PM_LOAD_ANIM_GLOBAL;
    page->priv.anim.attr.time = PM_ANIM_TIME_DEFAULT;
    page->priv.anim.attr.path = PM_ANIM_PATH_DEFAULT;
    
    PM_LOG_INFO("Page base created");
    return page;
}

/**
 * @brief  销毁页面基类
 * @param  page: 页面基类指针
 * @retval None
 */
void pm_page_base_destroy(pm_page_base_t* page)
{
    if (page == NULL) {
        return;
    }
    
    /* 释放stash数据 */
    if (page->priv.stash.ptr != NULL) {
        lv_mem_free(page->priv.stash.ptr);
        page->priv.stash.ptr = NULL;
        page->priv.stash.size = 0;
    }
    
    /* 如果根对象存在，删除它 */
    if (page->root != NULL) {
        lv_obj_del_async(page->root);
        page->root = NULL;
    }
    
    free(page);
    PM_LOG_INFO("Page base destroyed");
}

/**
 * @brief  设置自定义缓存启用状态
 * @param  page: 页面基类指针
 * @param  en: 是否启用缓存
 * @retval None
 */
void pm_page_base_set_custom_cache_enable(pm_page_base_t* page, bool en)
{
    if (page == NULL) {
        return;
    }
    
    PM_LOG_INFO("Page(%s) set_custom_cache_enable = %d", page->name, en);
    
    /* 禁用自动缓存管理 */
    pm_page_base_set_custom_auto_cache_enable(page, false);
    
    /* 设置手动缓存请求 */
    page->priv.req_enable_cache = en;
}

/**
 * @brief  设置自定义自动缓存启用状态
 * @param  page: 页面基类指针
 * @param  en: 是否启用自动缓存
 * @retval None
 */
void pm_page_base_set_custom_auto_cache_enable(pm_page_base_t* page, bool en)
{
    if (page == NULL) {
        return;
    }
    
    PM_LOG_INFO("Page(%s) set_custom_auto_cache_enable = %d", page->name, en);
    page->priv.req_disable_auto_cache = !en;
}

/**
 * @brief  设置自定义加载动画类型
 * @param  page: 页面基类指针
 * @param  anim_type: 动画类型
 * @param  time: 动画时间
 * @param  path: 动画路径
 * @retval None
 */
void pm_page_base_set_custom_load_anim_type(
    pm_page_base_t* page,
    uint8_t anim_type,
    uint16_t time,
    lv_anim_path_cb_t path)
{
    if (page == NULL) {
        return;
    }
    
    PM_LOG_INFO("Page(%s) set_custom_load_anim_type = %d", page->name, anim_type);
    
    page->priv.anim.attr.type = anim_type;
    page->priv.anim.attr.time = time;
    page->priv.anim.attr.path = path;
}

/**
 * @brief  从stash区域弹出数据
 * @param  page: 页面基类指针
 * @param  ptr: 数据指针
 * @param  size: 数据大小
 * @retval 成功返回true
 */
bool pm_page_stash_pop(pm_page_base_t* page, void* ptr, uint32_t size)
{
    if (page == NULL || ptr == NULL || size == 0) {
        PM_LOG_ERROR("Invalid parameters");
        return false;
    }
    
    if (page->priv.stash.ptr == NULL || page->priv.stash.size == 0) {
        PM_LOG_WARN("Page(%s) stash is empty", page->name);
        return false;
    }
    
    if (page->priv.stash.size != size) {
        PM_LOG_ERROR("Page(%s) stash size mismatch: expected %d, got %d", 
                     page->name, size, page->priv.stash.size);
        return false;
    }
    
    memcpy(ptr, page->priv.stash.ptr, size);
    PM_LOG_INFO("Page(%s) stash pop success, size = %d", page->name, size);
    
    return true;
}

/**
 * @brief  设置页面虚函数表
 * @param  page: 页面基类指针
 * @param  vtable: 虚函数表指针
 * @retval None
 */
void pm_page_base_set_vtable(pm_page_base_t* page, pm_page_vtable_t* vtable)
{
    if (page == NULL || vtable == NULL) {
        return;
    }
    
    page->vtable = vtable;
    PM_LOG_INFO("Page(%s) vtable set", page->name);
}

/**
 * @brief  获取页面虚函数表
 * @param  page: 页面基类指针
 * @retval 虚函数表指针
 */
pm_page_vtable_t* pm_page_base_get_vtable(pm_page_base_t* page)
{
    if (page == NULL) {
        return NULL;
    }
    
    return page->vtable;
}

/**
 * @brief  设置页面用户数据
 * @param  page: 页面基类指针
 * @param  user_data: 用户数据指针
 * @retval None
 */
void pm_page_base_set_user_data(pm_page_base_t* page, void* user_data)
{
    if (page == NULL) {
        return;
    }
    
    page->user_data = user_data;
}

/**
 * @brief  获取页面用户数据
 * @param  page: 页面基类指针
 * @retval 用户数据指针
 */
void* pm_page_base_get_user_data(pm_page_base_t* page)
{
    if (page == NULL) {
        return NULL;
    }
    
    return page->user_data;
}

/**
 * @brief  获取页面状态
 * @param  page: 页面基类指针
 * @retval 页面状态
 */
pm_page_state_t pm_page_base_get_state(pm_page_base_t* page)
{
    if (page == NULL) {
        return PM_PAGE_STATE_IDLE;
    }
    
    return page->priv.state;
}

/**
 * @brief  获取页面名称
 * @param  page: 页面基类指针
 * @retval 页面名称
 */
const char* pm_page_base_get_name(pm_page_base_t* page)
{
    if (page == NULL) {
        return PM_EMPTY_PAGE_NAME;
    }
    
    return page->name;
}

/**
 * @brief  获取页面根对象
 * @param  page: 页面基类指针
 * @retval 根对象指针
 */
lv_obj_t* pm_page_base_get_root(pm_page_base_t* page)
{
    if (page == NULL) {
        return NULL;
    }
    
    return page->root;
}

/**
 * @brief  获取页面管理器
 * @param  page: 页面基类指针
 * @retval 页面管理器指针
 */
pm_page_manager_t* pm_page_base_get_manager(pm_page_base_t* page)
{
    if (page == NULL) {
        return NULL;
    }
    
    return page->manager;
}
